'use client';

import { useAuth } from '../context/AuthContext';
import { useRouter } from 'next/navigation';
import HeroSection from './landing/HeroSection';
import FeaturesSection from './landing/FeaturesSection';
import TestimonialsSection from './landing/TestimonialsSection';
import PricingSection from './landing/PricingSection';
import FAQSection from './landing/FAQSection';
import Footer from './landing/Footer';

export default function LandingPage() {
  const { isAuthenticated, user } = useAuth();
  const router = useRouter();

  const handleGetStarted = () => {
    if (isAuthenticated) {
      router.push(user?.role === 'master' ? '/master/dashboard' : '/child/dashboard');
    } else {
      router.push('/auth/register');
    }
  };

  return (
    <div className="min-h-screen bg-background">
      <HeroSection
        isAuthenticated={isAuthenticated}
        user={user}
        onGetStarted={handleGetStarted}
      />
      <div id="features">
        <FeaturesSection />
      </div>
      <TestimonialsSection />
      <div id="pricing">
        <PricingSection />
      </div>
      <div id="faq">
        <FAQSection />
      </div>
      <Footer />
    </div>
  );
}
