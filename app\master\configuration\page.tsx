'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/app/context/AuthContext';
import ProtectedRoute from '@/app/components/auth/ProtectedRoute';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import Input from '@/app/components/Input';
import ZerodhaStatus from '@/app/components/ZerodhaStatus';
import { isConnectedToZerodha } from '@/app/utils/orderUtils';
import { motion } from 'framer-motion';
import { GlowingBorder } from '@/app/components/ui/animated-border';
import {
  Users,
  Mail,
  CheckCircle,
  AlertCircle,
  Settings,
  UserPlus,
  Shield,
  Key
} from 'lucide-react';

export default function MasterConfigurationPage() {
  const [childEmail, setChildEmail] = useState('');
  const [isInviting, setIsInviting] = useState(false);
  const [inviteSuccess, setInviteSuccess] = useState(false);
  const [inviteError, setInviteError] = useState('');
  const [childUsers, setChildUsers] = useState<any[]>([]);
  const [loadingChildren, setLoadingChildren] = useState(false);

  const { user, inviteChild, getChildUsers } = useAuth();

  // Load child users when component mounts
  useEffect(() => {
    const loadChildUsers = async () => {
      if (user && user.role === 'master') {
        setLoadingChildren(true);
        try {
          const children = await getChildUsers();
          setChildUsers(children);
        } catch (error) {
          console.error('Failed to load child users:', error);
        } finally {
          setLoadingChildren(false);
        }
      }
    };

    loadChildUsers();
  }, [user, getChildUsers]);

  // Check if Zerodha is connected using our utility function
  const isZerodhaConnected = isConnectedToZerodha();

  const handleInviteChild = async (e: React.FormEvent) => {
    e.preventDefault();
    setInviteSuccess(false);
    setInviteError('');
    setIsInviting(true);

    try {
      await inviteChild(childEmail);
      setInviteSuccess(true);
      setChildEmail('');

      // Reload child users after successful invitation
      const children = await getChildUsers();
      setChildUsers(children);
    } catch (error) {
      console.error('Invitation error:', error);
      setInviteError('Failed to send invitation. Please try again.');
    } finally {
      setIsInviting(false);
    }
  };

  return (
    <ProtectedRoute requiredRole="master">
      <div className="min-h-screen flex flex-col bg-background">
      {/* Animated background gradient */}
      <div className="fixed inset-0 bg-gradient-to-br from-blue-50/50 via-background to-purple-50/50 dark:from-blue-950/10 dark:via-background dark:to-purple-950/10" />

      {/* Animated gradient overlay */}
      <motion.div
        className="fixed inset-0 opacity-30 dark:opacity-20 pointer-events-none"
        animate={{
          background: [
            "radial-gradient(circle at 20% 50%, rgba(59, 130, 246, 0.1) 0%, transparent 50%)",
            "radial-gradient(circle at 80% 50%, rgba(139, 92, 246, 0.1) 0%, transparent 50%)",
            "radial-gradient(circle at 50% 80%, rgba(6, 182, 212, 0.1) 0%, transparent 50%)",
            "radial-gradient(circle at 20% 50%, rgba(59, 130, 246, 0.1) 0%, transparent 50%)"
          ]
        }}
        transition={{ duration: 10, repeat: Infinity, ease: "easeInOut" }}
      />

      <main className="flex-grow relative">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <motion.div
            className="flex items-center justify-between mb-8"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
          >
            <motion.h1
              className="text-3xl font-bold tracking-tight text-foreground sm:text-4xl"
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8, delay: 0.2 }}
            >
              Configuration
            </motion.h1>
          </motion.div>

          {/* Zerodha Status Component */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.4 }}
            className="mb-8"
          >
            <ZerodhaStatus />
          </motion.div>

          {/* Master Account Settings */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.6 }}
            className="mb-8"
          >
            <GlowingBorder className="h-full" glowColor="purple">
              <Card className="border-0 bg-background/50 backdrop-blur-sm">
                <CardHeader>
                  <div className="flex items-center space-x-3">
                    <motion.div
                      className="p-3 bg-gradient-to-r from-purple-500/10 to-pink-500/10 rounded-lg"
                      whileHover={{ scale: 1.1, rotate: 5 }}
                      whileTap={{ scale: 0.95 }}
                    >
                      <Settings className="w-6 h-6 text-purple-600" />
                    </motion.div>
                    <div>
                      <CardTitle className="text-xl font-semibold text-foreground">Master Account Settings</CardTitle>
                      <CardDescription className="text-muted-foreground">
                        Configure your trading account preferences
                      </CardDescription>
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="space-y-4">
                      <div>
                        <label className="text-sm font-medium text-foreground">Account Email</label>
                        <p className="text-sm text-muted-foreground mt-1">{user.email}</p>
                      </div>
                      <div>
                        <label className="text-sm font-medium text-foreground">Account Type</label>
                        <p className="text-sm text-muted-foreground mt-1">Master Trader</p>
                      </div>
                    </div>
                    <div className="space-y-4">
                      <div>
                        <label className="text-sm font-medium text-foreground">Zerodha Status</label>
                        <div className="mt-1">
                          {isZerodhaConnected ? (
                            <Badge className="bg-gradient-to-r from-green-500/20 to-emerald-500/20 border-green-300/50 text-green-700 dark:text-green-400">
                              <CheckCircle className="w-3 h-3 mr-1" />
                              Connected
                            </Badge>
                          ) : (
                            <Badge className="bg-gradient-to-r from-red-500/20 to-pink-500/20 border-red-300/50 text-red-700 dark:text-red-400">
                              <AlertCircle className="w-3 h-3 mr-1" />
                              Not Connected
                            </Badge>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </GlowingBorder>
          </motion.div>

          {/* Child Users Management */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.8 }}
            className="mb-8"
          >
            <GlowingBorder className="h-full" glowColor="blue">
              <Card className="border-0 bg-background/50 backdrop-blur-sm">
                <CardHeader>
                  <div className="flex items-center space-x-3">
                    <motion.div
                      className="p-3 bg-gradient-to-r from-blue-500/10 to-purple-500/10 rounded-lg"
                      whileHover={{ scale: 1.1, rotate: 5 }}
                      whileTap={{ scale: 0.95 }}
                    >
                      <Users className="w-6 h-6 text-blue-600" />
                    </motion.div>
                    <div>
                      <CardTitle className="text-xl font-semibold text-foreground">Child Users Management</CardTitle>
                      <CardDescription className="text-muted-foreground">
                        Invite and manage users who will copy your trades
                      </CardDescription>
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  {/* Invite Form */}
                  <form onSubmit={handleInviteChild} className="mb-6">
                    <div className="flex flex-col sm:flex-row gap-4">
                      <div className="flex-grow">
                        <Input
                          type="email"
                          placeholder="Enter email address"
                          value={childEmail}
                          onChange={(e) => setChildEmail(e.target.value)}
                          required
                          disabled={!isZerodhaConnected}
                          fullWidth
                          className="bg-background/50 border-2 border-transparent bg-gradient-to-r from-blue-500/10 via-purple-500/10 to-cyan-500/10 focus:from-blue-500/20 focus:via-purple-500/20 focus:to-cyan-500/20 transition-all duration-300"
                        />
                      </div>
                      <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
                        <Button
                          type="submit"
                          disabled={isInviting || !isZerodhaConnected}
                          className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 transition-all duration-300"
                        >
                          {isInviting ? (
                            <div className="flex items-center">
                              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                              Sending...
                            </div>
                          ) : (
                            <div className="flex items-center">
                              <Mail className="w-4 h-4 mr-2" />
                              Send Invitation
                            </div>
                          )}
                        </Button>
                      </motion.div>
                    </div>

                    {inviteSuccess && (
                      <motion.div
                        initial={{ opacity: 0, y: 10 }}
                        animate={{ opacity: 1, y: 0 }}
                        className="mt-4 p-3 bg-gradient-to-r from-green-500/10 to-emerald-500/10 border border-green-200 rounded-lg"
                      >
                        <div className="flex items-center text-green-700 dark:text-green-400">
                          <CheckCircle className="w-4 h-4 mr-2" />
                          Invitation sent successfully!
                        </div>
                      </motion.div>
                    )}

                    {inviteError && (
                      <motion.div
                        initial={{ opacity: 0, y: 10 }}
                        animate={{ opacity: 1, y: 0 }}
                        className="mt-4 p-3 bg-gradient-to-r from-red-500/10 to-pink-500/10 border border-red-200 rounded-lg"
                      >
                        <div className="flex items-center text-red-700 dark:text-red-400">
                          <AlertCircle className="w-4 h-4 mr-2" />
                          {inviteError}
                        </div>
                      </motion.div>
                    )}

                    {/* Only show warning if not connected to Zerodha */}
                    {!isZerodhaConnected && (
                      <motion.div
                        initial={{ opacity: 0, y: 10 }}
                        animate={{ opacity: 1, y: 0 }}
                        className="mt-4 p-3 bg-gradient-to-r from-amber-500/10 to-yellow-500/10 border border-amber-200 rounded-lg"
                      >
                        <div className="flex items-center text-amber-700 dark:text-amber-400">
                          <AlertCircle className="w-4 h-4 mr-2" />
                          You need to connect your Zerodha account before inviting child users.
                        </div>
                      </motion.div>
                    )}
                  </form>

                  {/* Child Users List */}
                  <div className="mt-6">
                    <h3 className="text-lg font-semibold text-foreground mb-4 flex items-center">
                      <Users className="w-5 h-5 mr-2 text-blue-600" />
                      Connected Child Users
                    </h3>

                    {loadingChildren ? (
                      <motion.div
                        initial={{ opacity: 0 }}
                        animate={{ opacity: 1 }}
                        className="p-6 bg-gradient-to-r from-blue-500/10 to-purple-500/10 rounded-lg text-center"
                      >
                        <div className="flex justify-center items-center text-muted-foreground">
                          <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-blue-500 mr-2"></div>
                          Loading child users...
                        </div>
                      </motion.div>
                    ) : childUsers.length > 0 ? (
                      <div className="space-y-3">
                        {childUsers.map((child, index) => (
                          <motion.div
                            key={index}
                            initial={{ opacity: 0, y: 20 }}
                            animate={{ opacity: 1, y: 0 }}
                            transition={{ duration: 0.4, delay: index * 0.1 }}
                          >
                            <GlowingBorder glowColor={index % 2 === 0 ? "purple" : "cyan"}>
                              <Card className="border-0 bg-background/50 backdrop-blur-sm">
                                <CardContent className="p-4">
                                  <div className="flex items-center justify-between">
                                    <div className="space-y-1">
                                      <p className="font-medium text-foreground">{child.childEmail}</p>
                                      <p className="text-sm text-muted-foreground">
                                        Zerodha ID: {child.zerodhaUserId}
                                      </p>
                                      <p className="text-xs text-muted-foreground">
                                        Connected: {new Date(child.connectedAt).toLocaleDateString()}
                                      </p>
                                    </div>
                                    <div className="flex items-center">
                                      <motion.div
                                        initial={{ scale: 0 }}
                                        animate={{ scale: 1 }}
                                        transition={{ delay: 0.5 + index * 0.1, type: "spring" }}
                                      >
                                        <Badge className="bg-gradient-to-r from-green-500/20 to-emerald-500/20 border-green-300/50 text-green-700 dark:text-green-400">
                                          <CheckCircle className="w-3 h-3 mr-1" />
                                          Connected
                                        </Badge>
                                      </motion.div>
                                    </div>
                                  </div>
                                </CardContent>
                              </Card>
                            </GlowingBorder>
                          </motion.div>
                        ))}
                      </div>
                    ) : (
                      <motion.div
                        initial={{ opacity: 0 }}
                        animate={{ opacity: 1 }}
                        className="p-6 bg-gradient-to-r from-gray-500/10 to-slate-500/10 rounded-lg text-center"
                      >
                        <div className="text-muted-foreground">
                          <Users className="w-8 h-8 mx-auto mb-2 opacity-50" />
                          No child users connected yet. Send an invitation to get started!
                        </div>
                      </motion.div>
                    )}
                  </div>
                </CardContent>
              </Card>
            </GlowingBorder>
          </motion.div>
        </div>
      </main>
    </div>
    </ProtectedRoute>
  );
}
