import { createServerClient } from '@supabase/ssr'
import { NextResponse, type NextRequest } from 'next/server'

export async function middleware(request: NextRequest) {
  let supabaseResponse = NextResponse.next({
    request,
  })

  const supabase = createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        getAll() {
          return request.cookies.getAll()
        },
        setAll(cookiesToSet) {
          cookiesToSet.forEach(({ name, value, options }) => request.cookies.set(name, value))
          supabaseResponse = NextResponse.next({
            request,
          })
          cookiesToSet.forEach(({ name, value, options }) =>
            supabaseResponse.cookies.set(name, value, options)
          )
        },
      },
    }
  )

  // IMPORTANT: Avoid writing any logic between createServerClient and
  // supabase.auth.getUser(). A simple mistake could make it very hard to debug
  // issues with users being randomly logged out.

  const {
    data: { user },
    error: authError,
  } = await supabase.auth.getUser()

  const { pathname } = request.nextUrl

  // Define public routes that don't require authentication
  const publicRoutes = [
    '/',
    '/auth/callback',
    '/auth/zerodha/callback',
    '/auth/zerodha/child-callback',
    '/auth/accept-invitation',
    '/api',
    '/demo/trading', // Allow demo trading without auth
  ]

  // Define protected routes that require authentication
  const protectedRoutes = [
    '/dashboard',
    '/master/dashboard',
    '/child/dashboard',
    '/master/configuration',
  ]

  // Check if current route is public
  const isPublicRoute = publicRoutes.some(route => {
    if (route === '/api') {
      return pathname.startsWith('/api/')
    }
    return pathname === route || pathname.startsWith(route + '/')
  })

  // Check if current route is protected
  const isProtectedRoute = protectedRoutes.some(route =>
    pathname.startsWith(route)
  )

  // If it's a public route, allow access
  if (isPublicRoute) {
    return supabaseResponse
  }

  // If it's a protected route, check authentication
  if (isProtectedRoute) {
    // Check for Supabase authentication first
    if (user && !authError) {
      // User is authenticated with Supabase, allow access
      return supabaseResponse
    }

    // If no Supabase user, check for legacy authentication in cookies
    // This handles users who authenticated via Zerodha before Supabase integration
    const legacyUserCookie = request.cookies.get('legacy_user')
    const zerodhaTokenCookie = request.cookies.get('zerodha_access_token')

    if (legacyUserCookie?.value || zerodhaTokenCookie?.value) {
      // User has legacy authentication, allow access
      // The client-side AuthContext will handle the authentication state
      return supabaseResponse
    }

    // No authentication found, redirect to landing page
    const redirectUrl = new URL('/', request.url)
    redirectUrl.searchParams.set('redirect', pathname)

    const redirectResponse = NextResponse.redirect(redirectUrl)

    // Copy over the Supabase cookies to maintain session state
    redirectResponse.cookies.setAll(supabaseResponse.cookies.getAll())

    return redirectResponse
  }

  // For all other routes, allow access
  return supabaseResponse
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - Static assets (images, fonts, etc.)
     * - API routes that handle their own auth
     */
    '/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp|ico|css|js|woff|woff2|ttf|eot)$).*)',
  ],
}
