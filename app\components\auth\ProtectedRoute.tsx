'use client';

import { useEffect, useState } from 'react';
import { useRouter, usePathname } from 'next/navigation';
import { useAuth } from '../../context/AuthContext';

interface ProtectedRouteProps {
  children: React.ReactNode;
  requiredRole?: 'master' | 'child';
  fallbackPath?: string;
}

export default function ProtectedRoute({ 
  children, 
  requiredRole,
  fallbackPath = '/' 
}: ProtectedRouteProps) {
  const { user, loading, isAuthenticated } = useAuth();
  const router = useRouter();
  const pathname = usePathname();
  const [isChecking, setIsChecking] = useState(true);

  useEffect(() => {
    // Wait for auth context to finish loading
    if (loading) return;

    // If user is not authenticated, redirect to landing page with redirect parameter
    if (!isAuthenticated || !user) {
      const redirectUrl = new URL(fallbackPath, window.location.origin);
      redirectUrl.searchParams.set('redirect', pathname);
      router.push(redirectUrl.toString());
      return;
    }

    // If a specific role is required, check if user has that role
    if (requiredRole && user.role !== requiredRole) {
      // Redirect to appropriate dashboard based on user's actual role
      const correctPath = user.role === 'master' ? '/master/dashboard' : '/child/dashboard';
      router.push(correctPath);
      return;
    }

    // All checks passed, allow access
    setIsChecking(false);
  }, [user, loading, isAuthenticated, requiredRole, router, pathname, fallbackPath]);

  // Show loading state while checking authentication
  if (loading || isChecking) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-background">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-muted-foreground">Verifying authentication...</p>
        </div>
      </div>
    );
  }

  // If we reach here, user is authenticated and authorized
  return <>{children}</>;
}
