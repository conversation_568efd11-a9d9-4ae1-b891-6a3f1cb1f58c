'use client';

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { supabase } from '../../lib/supabase';
import type { User as SupabaseUser } from '@supabase/supabase-js';

// Define user types
export type UserRole = 'master' | 'child';

export interface User {
  id: string;
  email: string;
  role: UserRole;
  name?: string;
  avatarUrl?: string; // Profile photo URL from Google OAuth or other sources
  zerodhaApiKey?: string;
  zerodhaApiSecret?: string;
  zerodhaAccessToken?: string;  // Legacy property
  zerodha_access_token?: string; // New property format
  zerodhaUserId?: string; // Zerodha user ID
  masterId?: string; // For child users, reference to their master
  masterEmail?: string; // For child users, master's email
  invitedEmail?: string; // Original invited email (may differ from Zerodha email)
  supabaseUser?: SupabaseUser; // Supabase user data
  isSupabaseAuth?: boolean; // Flag to indicate if user is authenticated via Supabase
}

// Define context type
interface AuthContextType {
  user: User | null;
  loading: boolean;
  setUserRole: (role: UserRole) => void;
  logout: () => void;
  connectToZerodha: () => void;
  updateUserEmail: (newEmail: string) => void;
  inviteChild: (email: string) => Promise<void>;
  getChildUsers: () => Promise<any[]>;
  isAuthenticated: boolean;
}

// Create context with default values
const AuthContext = createContext<AuthContextType>({
  user: null,
  loading: true,
  setUserRole: () => {},
  logout: () => {},
  connectToZerodha: () => {},
  updateUserEmail: () => {},
  inviteChild: async () => {},
  getChildUsers: async () => [],
  isAuthenticated: false,
});

// Create provider component
export const AuthProvider = ({ children }: { children: ReactNode }) => {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);

  // Check for stored user and Supabase session on mount
  useEffect(() => {
    const checkAuth = async () => {
      try {
        // First check for Supabase session
        const { data: { session } } = await supabase.auth.getSession();

        if (session?.user) {
          // User is authenticated with Supabase
          const supabaseUser: User = {
            id: session.user.id,
            email: session.user.email || '',
            role: (session.user.user_metadata?.role as UserRole) || 'master',
            name: session.user.user_metadata?.name || session.user.user_metadata?.full_name,
            avatarUrl: session.user.user_metadata?.avatar_url || session.user.user_metadata?.picture,
            supabaseUser: session.user,
            isSupabaseAuth: true,
          };
          setUser(supabaseUser);
          localStorage.setItem('user', JSON.stringify(supabaseUser));
        } else {
          // Check for legacy localStorage user (for Zerodha auth)
          const storedUser = localStorage.getItem('user');

          if (storedUser) {
            const parsedUser = JSON.parse(storedUser);

            // Check if we have a Zerodha access token in localStorage
            const zerodhaAccessToken = localStorage.getItem('zerodha_access_token');
            if (zerodhaAccessToken && !parsedUser.zerodha_access_token) {
              // Update user with the token
              parsedUser.zerodha_access_token = zerodhaAccessToken;
              localStorage.setItem('user', JSON.stringify(parsedUser));
            }

            setUser(parsedUser);
          }
        }
      } catch (error) {
        console.error('Authentication error:', error);
        localStorage.removeItem('user');
      } finally {
        setLoading(false);
      }
    };

    checkAuth();

    // Listen for Supabase auth changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        if (event === 'SIGNED_IN' && session?.user) {
          const supabaseUser: User = {
            id: session.user.id,
            email: session.user.email || '',
            role: (session.user.user_metadata?.role as UserRole) || 'master',
            name: session.user.user_metadata?.name || session.user.user_metadata?.full_name,
            avatarUrl: session.user.user_metadata?.avatar_url || session.user.user_metadata?.picture,
            supabaseUser: session.user,
            isSupabaseAuth: true,
          };
          setUser(supabaseUser);
          localStorage.setItem('user', JSON.stringify(supabaseUser));
        } else if (event === 'SIGNED_OUT') {
          // Only clear if it was a Supabase user
          const currentUser = localStorage.getItem('user');
          if (currentUser) {
            const parsedUser = JSON.parse(currentUser);
            if (parsedUser.isSupabaseAuth) {
              setUser(null);
              localStorage.removeItem('user');
            }
          }
        }
      }
    );

    return () => subscription.unsubscribe();
  }, []);

  // Set user role function (simplified for MVP)
  const setUserRole = (role: UserRole) => {
    const newUser: User = {
      id: Math.random().toString(36).substring(2, 9), // Generate a random ID
      email: `temp-${role}@copytrade.local`, // Temporary email that will be updated from Zerodha
      role: role,
      name: role === 'master' ? 'Master User' : 'Child User',
    };

    // Store user in localStorage
    localStorage.setItem('user', JSON.stringify(newUser));
    setUser(newUser);
  };

  // Logout function
  const logout = async () => {
    if (user?.isSupabaseAuth) {
      // Sign out from Supabase
      await supabase.auth.signOut();
    }
    localStorage.removeItem('user');
    setUser(null);
  };

  // Connect to Zerodha function
  const connectToZerodha = () => {
    if (!user) return;

    // Get API key from environment variable
    const apiKey = process.env.NEXT_PUBLIC_ZERODHA_API_KEY;

    if (!apiKey) {
      console.error('Zerodha API key not found in environment variables');
      return;
    }

    // Create additional parameters to pass through the login flow
    const redirectParams = encodeURIComponent(JSON.stringify({
      user_id: user.id,
      user_role: user.role
    }));

    // Construct Zerodha auth URL according to their documentation
    const authUrl = `https://kite.zerodha.com/connect/login?v=3&api_key=${apiKey}&redirect_params=${redirectParams}`;

    // Open Zerodha login page in the same window (as per typical OAuth flow)
    window.location.href = authUrl;
  };

  // Function to update user email (called after Zerodha authentication or for Supabase users)
  const updateUserEmail = (newEmail: string) => {
    if (!user) return;

    const updatedUser = {
      ...user,
      email: newEmail,
    };

    setUser(updatedUser);
    localStorage.setItem('user', JSON.stringify(updatedUser));
  };



  // Invite child function - simplified to just call the API endpoint
  const inviteChild = async (email: string) => {
    try {
      setLoading(true);

      if (!user) throw new Error('User not authenticated');
      if (user.role !== 'master') throw new Error('Only master users can invite children');

      // Make an API call to send an invitation email
      // The API endpoint will handle all the logic including token generation and email sending
      const response = await fetch('/api/invite', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          masterEmail: user.email,
          childEmail: email,
          masterId: user.id,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to send invitation');
      }

      const result = await response.json();
      console.log(`Invitation sent to ${email}:`, result.message);

      return result;
    } catch (error) {
      console.error('Invitation error:', error);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  // Get child users for the current master
  const getChildUsers = async (): Promise<any[]> => {
    try {
      if (!user || user.role !== 'master') {
        return [];
      }

      const response = await fetch(`/api/auth/child-register?masterId=${user.id}`);
      if (response.ok) {
        const data = await response.json();
        return data.relationships || [];
      }
      return [];
    } catch (error) {
      console.error('Error fetching child users:', error);
      return [];
    }
  };

  return (
    <AuthContext.Provider
      value={{
        user,
        loading,
        setUserRole,
        logout,
        connectToZerodha,
        updateUserEmail,
        inviteChild,
        getChildUsers,
        isAuthenticated: !!user,
      }}
    >
      {children}
    </AuthContext.Provider>
  );
};

// Create custom hook for using auth context
export const useAuth = () => useContext(AuthContext);
